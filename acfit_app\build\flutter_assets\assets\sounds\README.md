# Sound Files for AC Fit App

This directory should contain the following sound files:

1. `button_click.mp3` - A subtle click sound for button presses
2. `option_select.mp3` - A sound for when an option is selected in questionnaires
3. `scroll_tick.mp3` - A tick sound for scrolling through number pickers
4. `continue.mp3` - A success sound for continuing to the next screen

## Sound File Requirements

- Files should be in MP3 format for best compatibility
- Keep file sizes small (under 50KB each) to minimize app size
- Use subtle, non-intrusive sounds that enhance the user experience
- Ensure sounds are royalty-free or properly licensed for commercial use

## How to Add Sound Files

1. Place the sound files in this directory
2. Make sure the filenames match exactly what's expected by the SoundService
3. Test the sounds to ensure they're not too loud or distracting

## Fallback Behavior

If sound files are missing, the app will fall back to system sounds where possible.
